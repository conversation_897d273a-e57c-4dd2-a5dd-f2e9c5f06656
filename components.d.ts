/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    ACard: typeof import('ant-design-vue/es')['Card']
    Analysis: typeof import('./src/components/Analysis/index.vue')['default']
    ChatContent: typeof import('./src/components/ChatContent/index.vue')['default']
    ChatItem: typeof import('./src/components/ChatItem/index.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    Layout: typeof import('./src/components/Layout/index.vue')['default']
    LeftBar: typeof import('./src/components/LeftBar/index.vue')['default']
    ListItem: typeof import('./src/components/ListItem/index.vue')['default']
    MeetingItem: typeof import('./src/components/Meeting/MeetingItem.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./src/components/icon/SvgIcon.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDatetimePicker: typeof import('vant/es')['DatetimePicker']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanIcon: typeof import('vant/es')['Icon']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPickerGroup: typeof import('vant/es')['PickerGroup']
    VanPopup: typeof import('vant/es')['Popup']
    VanSwitch: typeof import('vant/es')['Switch']
    VanTag: typeof import('vant/es')['Tag']
    VanTimePicker: typeof import('vant/es')['TimePicker']
  }
}
