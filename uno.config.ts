// uno.config.ts
import { defineConfig } from 'unocss'
import presetWind from '@unocss/preset-wind'
export default defineConfig({
  // ...UnoCSS选项
  presets: [presetWind()],
  theme: {
    colors: {
      colorPrimary: '#0066ff',
      colorSuccess: '#0d9e3d',
      colorError: '#ff5219',
      colorInfo: '#0066ff',
      colorText: '#242424',
      colorTextSecondary: 'rgba(0,0,0,0.6)',
      colorTextTertiary: 'rgba(0, 0, 0, 0.45)',
      colorTextQuaternary: 'rgba(0, 0, 0, 0.25)'
    }
  }
})
