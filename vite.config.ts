import { resolve } from 'path'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
// import viteCompression from 'vite-plugin-compression';
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
// import { VitePWA } from 'vite-plugin-pwa'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'path'
import legacy from '@vitejs/plugin-legacy'
import UnoCSS from 'unocss/vite'
import { createStyleImportPlugin } from 'vite-plugin-style-import'
import zipPack from 'vite-plugin-zip-pack'
import AutoImport from 'unplugin-auto-import/vite'
import {nodePolyfills} from 'vite-plugin-node-polyfills';

// const c = (compresssionBuild as any ).default
export default ({ mode }) => {
  // console.log('process.env', loadEnv(mode, process.cwd()))
  const env = loadEnv(mode, process.cwd())
  return defineConfig({
    plugins: [
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/ // .md
        ],
        imports: ['vue-i18n', 'vue', 'vue-router'],
        resolvers: [VantResolver()],
        dts: 'src/auto-imports.d.ts'
      }),

      UnoCSS(),
      vueJsx(),
      // VitePWA(),
      vue(),
      nodePolyfills({
        include: ['crypto'],
      }),

      zipPack({
        outDir: 'dist'
      }),

      Components({
        resolvers: [
          VantResolver(),
          AntDesignVueResolver({
            importStyle: false // css in js
          })
        ]
      }),
      // createStyleImportPlugin({
      //   // resolves:[VantResolve()]
      //   libs: [
      //     {
      //       libraryName: 'vant',
      //       esModule: false,
      //       resolveStyle: (name: string) => {
      //         console.log(name)
      //         return `vant/lib/${name}/style/index.js`
      //       }
      //     }
      //   ]
      // }),
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
        // svgoOptions: true,
        // default
        symbolId: 'icon-[dir]-[name]'
      }),
      legacy({
        targets: ['Chrome 84'],
        modernPolyfills: true,
        polyfills: ['es.global-this']
      })
    ],
    build: {
      reportCompressedSize: true,
      cssCodeSplit: true,
      target: ['ios11'],

      commonjsOptions: {
        transformMixedEsModules: true
      }
    },
    base: './',
    resolve: {
      alias: [
        {
          find: '@',
          replacement: resolve(__dirname, './src')
        },
        {
          find: 'assets',
          replacement: resolve(__dirname, './src/assets')
        },
        {
          find: 'vue',
          replacement: 'vue/dist/vue.esm-bundler.js' // compile template
        },
        {
          find: 'stream',
          replacement: 'stream-browserify' // compile template
        },
        {
          find: 'crypto',
          replacement: 'crypto-browserify' // compile template
        },
        {
          find: 'safe-buffer',
          replacement: 'safe-buffer' // compile template
        }
        // process: "process/browser",
        // stream: "stream-browserify",
        // zlib: "browserify-zlib",
        // util: 'util'
      ],
      extensions: ['.ts', '.js', '.mjs']
    },
    optimizeDeps: {},

    server: {
      host: '0.0.0.0',
      port: 5174,
      proxy: {
        '/synthai': {
          target: env.VITE_BASE_URL,
          changeOrigin: true
        }
      }
    }
  })
}
