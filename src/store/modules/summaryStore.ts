import { defineStore } from 'pinia'
import { store } from '@/store/index'
import type { ListItem } from '/#/index'

export const useSummaryStore = defineStore('SummaryStore', () => {
  const currentSummaryChat = reactive<ListItem>({} as ListItem)
  const refreshList = ref<boolean>(false)
  const setRefreshList = (value: boolean) => {
    refreshList.value = value
  }

  const setCurrentSummaryChat = (chat: ListItem) => {
    Object.assign(currentSummaryChat, chat)
  }

  const currentSummaryId = computed(() => currentSummaryChat.id)

  return {
    currentSummaryId,
    currentSummaryChat,
    setCurrentSummaryChat,
    refreshList,
    setRefreshList,
  }
})
export function useSummaryStoreWithOut() {
  return useSummaryStore(store)
}
