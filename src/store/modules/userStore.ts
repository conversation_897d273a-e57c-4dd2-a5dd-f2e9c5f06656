import { defineStore } from 'pinia'
import { store } from '@/store/index'
import type { LocaleType } from '/#/config'

export interface UserInfo {
  userName: string
  uid: string
  headUrl: string
  ssoToken: string,
  token: string,
  deviceInfo?: DeviceInfo
  [key: string]: any,
}
export interface DeviceInfo {
    appId: string,
    appKey: string,
    deviceId: string,
    sysType: string,
    version: string,
    [key: string]: any,
}
export interface UserStore {
  locale: LocaleType
  userInfo: UserInfo
}
export const userStore = defineStore('userStore', {
  state: (): UserStore => {
    return {
      locale: 'zh-CN',
      userInfo: { uid: '', userName: '', headUrl: '', ssoToken: '', token: '' }
    }
  },
  getters: {
    getLocale(): LocaleType {
      return this.locale ?? 'zh-CN'
    },
    getAccessToken(): string {
      return this.userInfo.token ?? ''
    },
    deviceInfo():DeviceInfo {
      return (this.userInfo?.deviceInfo || {}) as DeviceInfo
    }
  },
  actions: {
    setLocale(language: LocaleType) {
      this.locale = language
    },
    async setUserInfo(info: UserInfo) {
      this.userInfo = info
    },
  }
})

export function useUserStoreWithOut() {
  return userStore(store)
}
