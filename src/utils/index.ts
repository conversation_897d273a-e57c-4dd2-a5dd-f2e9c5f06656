import { isTLinkClient } from './is'
import { cordovaMideaUser } from './cordova'
import {decryptByDES} from './crypto'


// import CryptoJS from "crypto";


// const CryptoJS = require('crypto-browserify')
/**
 * @param { Promise } promise
 * @param { Object= } errorExt - Additional Information you can pass to the err object
 * @return { Promise }
 */
export function to<T, U = Error>(
  promise: Promise<T>,
  errorExt?: object
): Promise<[U, undefined] | [null, T]> {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[U, undefined]>((err: U) => {
      if (errorExt) {
        const parsedError = Object.assign({}, err, errorExt)
        return [parsedError, undefined]
      }

      return [err, undefined]
    })
}

export const getToekn = () => {
  if (isTLinkClient()) {
    return
  }
}

export const createUrl = (url: string): URL => {
  return new URL(url)
}

export const getHash = (url: string): string => {
  return createUrl(url).hash
}

export const getHashSearch = (url: string): string => {
  return getHash(url).split('?')[1] || ''
}

export const getHashSearhObj = (url: string): Object => {
  const searchObj: {
    [prop in string]: any
  } = {}
  new URLSearchParams(getHashSearch(url)).forEach(
    (val: string, key: string) => {
      searchObj[key] = val
    }
  )
  return searchObj
}

// 重URL获取token
export const getUrlToken = (url: string): string | null => {
  return new URLSearchParams(url).get('accessToken')
}

