import { ajax, type AjaxConfig } from 'rxjs/ajax'
import { map, timeout, catchError, switchMap } from 'rxjs/operators'
import { throwError, of, Observable } from 'rxjs'
import { useUserStoreWithOut } from '@/store/modules/userStore'

import qs from 'qs'
import type { Result } from '/#/axios'
const OK_CODES = ['0', 'OK']
const userStore = useUserStoreWithOut()
export interface CreateAjaxConfig extends AjaxTransform {
  retryNumer?: number
  timeout?: number
}

interface RequestConfig extends AjaxConfig, CreateAjaxConfig {}

export abstract class AjaxTransform {
  beforeRequestHook?: (config: RequestConfig) => RequestConfig
  transformResponseHook?: <T>(data: Result<T>) => Observable<unknown>
  requestCatchHook?: (err: unknown) => void
}
export class Http {
  timeout = 10 * 1000
  options: CreateAjaxConfig | undefined
  constructor(options?: CreateAjaxConfig) {
    this.options = options
  }
  post<T>(
    url: string,
    data: object = {},
    options?: Omit<RequestConfig, 'url'>
  ) {
    return this.request<T>(
      Object.assign({
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: qs.stringify(data),
        url,
        ...(options ?? {})
      })
    )
  }
  postJson<T>(
    url: string,
    data?: object,
    options?: Omit<RequestConfig, 'url'>
  ) {
    return this.post<T>(url, data, {
      headers: {
        'Content-Type': 'application/json; charset=utf-8'
      },

      body: JSON.stringify(data),
      ...(options ?? {})
    })
  }
  get<T>(
    url: string,
    params?: Record<
      string,
      string | number | boolean | string[] | number[] | boolean[]
    >,
    options: Omit<RequestConfig, 'url'> = {}
  ) {
    return this.request<T>({
      method: 'GET',
      url,
      queryParams: params,
      ...options
    })
  }

  request<T>(config: RequestConfig) {
    const beforeRequestHook =
      config.beforeRequestHook || this.options?.beforeRequestHook
    if (beforeRequestHook) {
      config = beforeRequestHook(config)
    }
    config.headers = config.headers ?? {}

    if (userStore.getAccessToken) {
      config.headers = Object.assign(config.headers ?? {}, {
        accessToken: userStore.getAccessToken ?? ''
      })
    }
    return ajax<Result<T>>(config).pipe(
      timeout(this.timeout),
      map(el => el.response),
      switchMap(res => {
        const transformResponseHook =
          config.transformResponseHook || this.options?.transformResponseHook
        if (transformResponseHook) {
          return transformResponseHook<T>(res)
        }
        return of(res)
      }),
      catchError(err => {
        const requestCatchHook =
          config.requestCatchHook || this.options?.requestCatchHook
        if (requestCatchHook) {
          requestCatchHook(err)
        }
        throw err
      })
    )
  }
}
interface Err extends Result, Error {}
const transform: AjaxTransform = {
  transformResponseHook(res) {
    if (!OK_CODES.includes(res?.code)) {
      return throwError(() => res)
    }
    return of(res.data)
  },
  requestCatchHook(err) {
    const e = err as Err
    const msg = e?.msg || e?.message
    console.log('统一报错', msg)
  }
}

export const http = new Http(transform)
