import CryptoJS from 'crypto-js'
import { Buffer } from 'buffer'

/**
 * 加密
 * @params message: 加密内容
 * @params keyStr: 秘钥
 * @params type: 类型
 * @params isPadding: 是否增加填充方式
 */
function encrypt(
    message: string,
    keyStr: string,
    type: 'DES' | 'AES',
    extral?: {
      isPadding?: boolean
      encType?: 'Utf8' | 'Base64'
    }
  ): string {
    const initIv = '1234567899543210'
    const key = CryptoJS.enc.Utf8.parse(keyStr)
    const buffer = Buffer.from([1, 2, 3, 4, 5, 6, 7, 8])
    const iv = extral?.isPadding
      ? CryptoJS.enc.Utf8.parse(initIv)
      : CryptoJS.lib.WordArray.create(buffer)
    const cfg = extral?.isPadding
      ? { iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 }
      : { iv }
    const cipher = CryptoJS[type].encrypt(message, key, cfg)
    return cipher.toString()
  }

  export function encryptByDES(message: string) {
    return encrypt(message, '471d242f1b32021ee8a2ee41d55fdf13', 'AES', {
      isPadding: true,
      encType: 'Base64'
    })
  }