import { isTLinkClient, isTLinkPC, isTLinkMobile } from './is'
import {LOCALE} from '@/config/localeSetting'
import type { LocaleType } from '/#/config'
import type { UserInfo } from '@/store/modules/userStore'
const { callPc } = window

export const getLinkPCuserInfo = async (): Promise<UserInfo> => {
  // console.log('PC用户信息')
  const res = await callPc.callTlinkpc('getUserInfo_cb')
  // console.log('PC用户信息', res)
  return res
}

export const getpcLanguage = async (): Promise<LocaleType> => {
  let userAgent = navigator.userAgent
  if (userAgent.includes('systemLang/')) {
    return userAgent.split('systemLang/')[1].replace('_', '-') as LocaleType
  }
  return LOCALE['ZH_CN']
}
