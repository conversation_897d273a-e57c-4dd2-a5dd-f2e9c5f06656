import Cookies from "js-cookie";
import type { UserInfo } from '@/store/modules/userStore'
import { decryptByDES } from './crypto'


// 加密
export const encryptByDES = (txt: any) => {
    let key = "3de12d03";
    let keyHex = new Buffer(key);
    let iv = new Buffer([1, 2, 3, 4, 5, 6, 7, 8]);
    // @ts-ignore
    let cipher = CryptoJS.createCipheriv("des-cbc", keyHex, iv);
    cipher.setAutoPadding(true);
    let ciph = cipher.update(txt, "utf8", "base64");
    ciph += cipher.final("base64");
    return ciph;
  };

// 设置cookie
export const setCookie = (name: string, value: string, expires = 2, path = "/") => {
    Cookies.set(name, value, { expires, path });
  };

  // 判断是json串
export const isJsonOrString = (str: any) => {
    try {
      if (typeof JSON.parse(str) == "object") {
        return true;
      }
    } catch (e) {}
    return false;
  };
  
  
  // 获取cookie
  export const getCookie = (name: string) => {
    if (name) {
      return Cookies.get(name);
    } else {
      return Cookies.get();
    }
  };
  
  // 移除cookie
  export const removeCookie = (name: string) => {
    Cookies.remove(name);
  };

  // 移除cookie对象
export const removeCookieInfo = () => {
    removeCookie("accessToken");
    removeCookie("userInfo");
    removeCookie("appPackage");
    removeCookie("lang");
};

// 获取cookie对象
export const getCookieInfo = () => {
    let userInfo = {};
    let accessToken = getCookie("accessToken");
    let userInfoStr = getCookie("userInfo");
    let appPackage = getCookie("appPackage");
    let lang = getCookie("lang");

    if (userInfoStr) {
        console.info('userInfoStr', userInfoStr)
        userInfoStr = decryptByDES(userInfoStr as string);
        if (isJsonOrString(userInfoStr)) {
            userInfo = JSON.parse(userInfoStr as string);
        }
    }
    return { accessToken, userInfo, appPackage, lang };
  };

export const getWebUserInfo = async (): Promise<UserInfo> => {
  let { accessToken, userInfo } = getCookieInfo();
  const userInfoObj = userInfo as UserInfo
  console.info('userInfoObj',accessToken, userInfoObj)
  return {
    ssoToken: accessToken as string ,
    token: accessToken as string,
    headUrl: userInfoObj.headUrl,
    uid: userInfoObj.uid,
    userName: userInfoObj.userName,
  }
}
