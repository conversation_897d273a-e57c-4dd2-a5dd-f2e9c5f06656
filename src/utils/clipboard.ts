import { showToast } from 'vant'
import Clipboard from 'clipboard'
export default function handleClipboard(text: string, event: Event) {
  // console.log(event)
  const clipboard = new Clipboard(event.target as Element, {
    text: () => text
  })
  clipboard.on('success', () => {
    //   clipboardSuccess()
    showToast('复制成功')
    clipboard.destroy()
  })
  clipboard.on('error', () => {
    //   clipboardError()
    showToast('复制失败')

    clipboard.destroy()
  })
  ;(clipboard as any).onClick(event)
}
