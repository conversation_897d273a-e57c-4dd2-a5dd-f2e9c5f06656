import { isTLinkMobile } from '@/utils/is'
import { to } from '@/utils/index'
import type { UserInfo } from '@/store/modules/userStore'
import { LOCALE } from '@/config/localeSetting'
import type { LocaleType } from '/#/config'
const { cordova } = window

const MAX_DO_NUM = 5

// 监听设备加载情况
function holdForDevice(): any {
  console.log('执行')
  return new Promise((resolve, reject) => {
    if (isTLinkMobile()) {
      //等设备加载完毕后执行
      if (!cordova) {
        return reject('获取cordova失败')
      }
      document.addEventListener(
        'deviceready',
        () => {
          console.log('成功')
          resolve('成功')
        },
        false
      )
    } else {
      reject('只能在T信移动端调用')
      console.log('失败')
    }
  })
}

const cordovaPromise = holdForDevice()

// 通用方法
export async function cordovaExec<T = unknown>(
  classify: string,
  method: string,
  params?: T[]
): Promise<unknown> {
  const [err] = await to(cordovaPromise)
  if (err) return Promise.reject(err)
  return new Promise((resolve, reject) => {
    cordova.exec(
      res => {
        resolve(res)
      },
      err => {
        reject(err)
      },
      classify,
      method,
      params
    )
  })
}
// 通用插件
export async function cordovaMideaCommon<T = unknown>(
  method: string,
  params?: T[]
) {
  return cordovaExec('MideaCommon', method, params)
}
// 用户插件
export async function cordovaMideaUser<T = unknown>(
  method: string,
  params?: T[]
) {
  return cordovaExec('MideaUser', method, params)
}

// 获取用户信息
export async function cordovaGetUser(): Promise<UserInfo> {
  console.log('调用cordovaGetUser')
  const obj: any = await cordovaMideaUser('getUser', [])
  console.log('调用cordovaGetUser', obj)
  obj.sessionKey = obj.sessionkey

  obj.sessionKey = obj.sessionKey ?? obj.token
  obj.uphone = obj.uphone ?? obj.mobile

  obj.gender = obj.gender ?? obj.midea_gender
  obj.usex = obj.gender === 'M' ? '男' : '女'
  obj.uname = obj.cn
  return obj
}

export async function cordovaGetLanguage(): Promise<LocaleType> {
  const res: any = await cordovaMideaCommon('language', [])
  let lan = res.language as string

  if (lan === 'en') return LOCALE['EN_US']

  return LOCALE['ZH_CN']
}
