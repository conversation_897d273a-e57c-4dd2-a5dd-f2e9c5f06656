// @ts-ignore
import crypto from 'crypto'

const Buffer = (window as any).Buffer as any

export function encryptByDES(txt: string) {
  let key = '3de12d03'
  let keyHex = new Buffer(key)
  let iv = new Buffer([1, 2, 3, 4, 5, 6, 7, 8])
  let cipher = crypto.createCipheriv('des-cbc', keyHex, iv)
  cipher.setAutoPadding(true)
  let ciph = cipher.update(txt, 'utf8', 'base64')
  ciph += cipher.final('base64')
  return ciph
}

export function decryptByDES(enTxt: string) {
  let key = '3de12d03'
  let keyHex = new Buffer(key)
  let iv = new Buffer([1, 2, 3, 4, 5, 6, 7, 8])
  let decipher = crypto.createDecipheriv('des-cbc', keyHex, iv)
  decipher.setAutoPadding(true)
  let txt = decipher.update(enTxt, 'base64', 'utf8')
  txt += decipher.final('utf8')
  return txt
}

