import { APP_NAME } from '@/config/index'

export function isUrl(path: string): boolean {
  const reg =
    /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/
  return reg.test(path)
}
// 判断是不是移动端
export function IsPC(): boolean {
  const userAgentInfo = navigator.userAgent
  const agents = [
    'Android',
    'iPhone',
    'SymbianOS',
    'Windows Phone',
    'iPad',
    'iPod',
  ]
  let flag = true
  for (let v = 0; v < agents.length; v++) {
    if (userAgentInfo.indexOf(agents[v]) > 0) {
      flag = false
      break
    }
  }
  return flag
}

// 判断是否为电脑或iPad
export function isPCOrIPad(): boolean {
  const userAgentInfo = navigator.userAgent
  const isIPad = userAgentInfo.indexOf('iPad') > 0
  
  // 检查是否为PC（排除常见移动设备）
  const mobileAgents = [
    'Android',
    'iPhone',
    'SymbianOS',
    'Windows Phone',
    'iPod',
  ]
  
  let isMobile = false
  for (let v = 0; v < mobileAgents.length; v++) {
    if (userAgentInfo.indexOf(mobileAgents[v]) > 0) {
      isMobile = true
      break
    }
  }
  
  return !isMobile || isIPad
}


// 判断是不是T信客户端
export function isTLinkClient(): boolean {
  const userAgent = navigator.userAgent
  let flag = false
  if (userAgent.includes(APP_NAME)) {
    flag = true
  }
  return flag
}
// 判断是不是T信移动客户端
export function isTLinkMobile(): boolean {
  let flag = false
  if (!IsPC() && isTLinkClient()) {
    flag = true
  }
  return flag
}
// 判断是不是T信PC端
export function isTLinkPC(): boolean {
  let flag = false
  if (IsPC() && isTLinkClient()) {
    flag = true
  }
  return flag
}

// 获取设备信息，判断是不是iphoneX
export function isIphoneX(): boolean {
  // 这个条件成立即为iphoneX
  let screenWidth = window.screen.width
  let screenHeight = window.screen.height
  if (
    isIosOrAndroid() == 'ios' &&
    (screenWidth == 375 || screenWidth == 414) &&
    (screenHeight == 812 || screenHeight == 896)
  ) {
    return true
  } else {
    return false
  }
}
// 获取设备信息，判断是安卓还是苹果
export function isIosOrAndroid(): string {
  let u = navigator.userAgent
  let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1
  let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
  let device = ''
  if (isAndroid) {
    //这个是安卓操作系统
    device = 'android'
  } else if (isIOS) {
    //这个是ios操作系统
    device = 'ios'
  } else {
    device = 'other'
  }
  return device
}
// 判断微信内核浏览器
export function isWeChatClient(): boolean {
  const userAgent = navigator.userAgent
  let flag = false
  if (userAgent.includes('MicroMessenger')) {
    flag = true
  }
  return flag
}

/*判断是否为图片*/
export function isImageFile(file: string): boolean {
  let reg = /\.jpg|\.jpeg|\.png|\.webp|\.gif|\.tiff|\.bmp|\.svg$/i
  if (file.match(reg)) {
    return true
  } else {
    return false
  }
}

/*判断是否为音视频*/
export function isAudioVideoFile(file: string): boolean {
  let reg =
    /\.wmv|\.avi|\.dat|\.asf|\.mpeg|\.mpg|\.rm|\.rmvb|\.ram|\.flv|\.mp4|\.3gp|\.mov|\.divx|\.dv|\.vob|\.mkv|\.qt|\.cpk|\.fli|\.flc|\.f4v|\.m4v|\.mod|\.m2t|\.swf|\.webm|\.mts|\.m2ts|\.3g2|\.mpe|\.ts|\.div|\.lavf|\.dirac|\.div|\.cda|\.wav|\.mp3|\.wma|\.ra|\.midi|\.ogg|\.ape|\.flac|\.aac|\.amr|\.vqf$/i
  if (file.match(reg)) {
    return true
  } else {
    return false
  }
}
