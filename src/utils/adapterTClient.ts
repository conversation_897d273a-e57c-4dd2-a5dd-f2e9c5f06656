// 适配 cordova 和 tlinkpc 交互方法

import { cordovaMideaUser, cordovaGetUser, cordovaGetLanguage } from './cordova'
import type { UserInfo } from '@/store/modules/userStore'
import { getLinkPCuserInfo, getpcLanguage } from './tlinkpc'
import { useUserStoreWithOut } from '@/store/modules/userStore'
import { isTLinkClient, isTLinkPC, isTLinkMobile } from './is'
import { LOCALE } from '@/config/localeSetting'
import type { LocaleType } from '/#/config'
import {setI18nLanguage} from '@/locales/index'
import { getWebUserInfo } from './cookie'

const isTLink_PC = isTLinkPC()
const isTLink_mobile = isTLinkMobile()
export async function getUserInfo(): Promise<UserInfo> {
  if (isTLink_mobile) {
    return cordovaGetUser()
  } else if (isTLink_PC) {
    try {
      return await getLinkPCuserInfo()
    } catch (error) {
      return getWebUserInfo()
    }
  }
  return {
    userName: import.meta.env.VITE_USER_NAME,
    uid: import.meta.env.VITE_UID,
    headUrl: '',
    ssoToken: import.meta.env.VITE_SSOTOKEN,
    token: import.meta.env.VITE_SSOTOKEN
  }
}

export async function setupUserInfo(): Promise<UserInfo> {
  const useUserStore = useUserStoreWithOut()
  const info = await getUserInfo()
  useUserStore.setUserInfo(info)
  return info
}


export async function setupLanguage():Promise<LocaleType> {
  let lang: LocaleType = LOCALE['ZH_CN']
  if (isTLink_mobile) {
    lang = await cordovaGetLanguage()
  } else if (isTLink_PC) {
    lang = await getpcLanguage()
  }
  setI18nLanguage(lang)
  return lang
}
