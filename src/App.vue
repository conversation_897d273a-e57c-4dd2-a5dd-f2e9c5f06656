<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()
const router = useRouter()

console.log('i18n', t('sys.confirmTxt'))
console.log(import.meta.env.VITE_BASE_URL)

</script>

<template>
  <RouterView></RouterView>
</template>

<script lang="ts">
// 添加全局函数声明
declare global {
  function emitAction(callback: () => void): void
}
</script>

<style>
/* .logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
} */
</style>
