<template>
  <main
    class="flex items-center justify-center w-auto h-full overflow-hidden bg-white rounded-4px"
  >
    <div class="text-center p-8">
      <h1 class="text-2xl font-bold text-gray-800 mb-8">Yealink H5 会议系统</h1>
      
      <div class="space-y-4">
        <van-button 
          type="primary" 
          size="large" 
          round 
          class="w-48"
          @click="goToMeetingList"
        >
          进入会议中心
        </van-button>
        
        <div class="flex justify-center gap-4 mt-6">
          <van-button 
            type="default" 
            size="normal" 
            round
            @click="goToJoinMeeting"
          >
            快速加入
          </van-button>
          
          <van-button 
            type="default" 
            size="normal" 
            round
            @click="goToScheduleMeeting"
          >
            预约会议
          </van-button>
        </div>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToMeetingList = () => {
  router.push('/meeting')
}

const goToJoinMeeting = () => {
  router.push('/meeting/join')
}

const goToScheduleMeeting = () => {
  router.push('/meeting/schedule')
}
</script>

<style lang="less" module>

</style>
