<template>
  <div>
    icon页面
    <p>{{ t('sys.alertTitle') }}</p>
    <SvgIcon v-for="el in icons" :key="el" :name="el" size="32" />
  </div>
</template>
<script setup lang="ts">
// import HelloWorld from './components/HelloWorld.vue'
import { last } from 'lodash-es'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
const icons = Object.keys(
  import.meta.glob('../../assets/icons/*.svg')
).map(el => last(el.replace('.svg', '').split('/')))
console.log(icons)
</script>

<style></style>
