<template>
  <div class="schedule-meeting-page bg-gray-100 min-h-screen">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="预约会议"
      left-arrow
      @click-left="onBack"
      class="bg-white"
    >
      <template #right>
        <span class="text-blue-500 text-sm" @click="handleSubmit">完成</span>
      </template>
    </van-nav-bar>

    <div class="p-4 space-y-4">
      <!-- 会议主题 -->
      <div class="bg-white rounded-md p-1">
        <van-field
          v-model="form.title"
          :placeholder="form.title ? '' : '请输入会议主题'"
          clearable
        />
      </div>

      <!-- 会议信息 -->
      <div>
        <div class="text-sm font-medium text-gray-800 mb-3">会议信息</div>
        <div class="bg-white rounded-lg">
          <!-- 时间设置 -->
          <div class="py-3 border-b border-gray-100 text-center">
            <div class="flex justify-between items-center">
              <!-- Start Time Column -->
              <div class="flex-1" @click="openStartTimePicker" role="button">
                <div class="text-xl font-medium text-gray-900">
                  {{ form.startTime }}
                </div>
                <div class="text-xs text-gray-500 mt-1">
                  {{ form.startDate }} {{ form.startDayOfWeek }}
                </div>
              </div>
              <!-- Separator / Duration Column -->
              <div class="flex-1">
                <img src="@/assets/image/time.png" alt="持续" class="w-8 h-8">
                <!-- <div
                  class="text-xs text-gray-500 mt-1 flex items-center justify-center"
                >
                  <van-icon name="clock-o" size="12" class="mr-1" />
                  持续{{ meetingDuration }}
                </div> -->
              </div>
              <!-- End Time Column -->
              <div class="flex-1" @click="openEndTimePicker" role="button">
                <div class="text-xl font-medium text-gray-900">
                  {{ form.endTime }}
                </div>
                <div class="text-xs text-gray-500 mt-1">
                  {{ form.endDate }} {{ form.endDayOfWeek }}
                </div>
              </div>
            </div>
          </div>

          <!-- 会议频率 -->
          <van-cell-group inset>
            <van-cell
              title="会议频率"
              :value="selectedFrequencyText"
              is-link
              @click="showFrequencyPicker = true"
            />
          </van-cell-group>
        </div>
      </div>
      <!-- 入会设置 -->
      <div>
        <div class="text-sm font-medium text-gray-800 mb-3">入会设置</div>
        <div class="space-y-0 bg-white rounded-md">
          <van-cell-group inset>
            <van-field
              v-model="form.password"
              label="会议密码"
              placeholder="请输入4-6位密码（选填）"
              type="password"
              maxlength="6"
              input-align="right"
            />

            <van-cell
              title="入会限制"
              :value="selectedJoinRestrictionText"
              is-link
              @click="showJoinRestrictionPicker = true"
            />
          </van-cell-group>
        </div>
      </div>
    </div>

    <!-- 开始时间选择器 -->
    <van-popup v-model:show="showStartTimePickerState" position="bottom">
      <van-picker-group
        title="选择会议开始时间"
        :tabs="['选择日期', '选择时间']"
        next-step-text="下一步"
        @confirm="onStartTimeConfirm"
        @cancel="showStartTimePickerState = false"
      >
        <van-date-picker
          v-model="currentStartDateArray"
          :min-date="minCalendarDate"
        />
        <van-time-picker v-model="currentStartTimeArray" />
      </van-picker-group>
    </van-popup>

    <!-- 结束时间选择器 -->
    <van-popup v-model:show="showEndTimePickerState" position="bottom">
      <van-picker-group
        title="选择会议结束时间"
        :tabs="['选择日期', '选择时间']"
        next-step-text="下一步"
        @confirm="onEndTimeConfirm"
        @cancel="showEndTimePickerState = false"
      >
        <van-date-picker v-model="currentEndDateArray" :min-date="minEndDate" />
        <van-time-picker v-model="currentEndTimeArray" />
      </van-picker-group>
    </van-popup>

    <!-- 会议频率选择器 -->
    <van-action-sheet
      v-model:show="showFrequencyPicker"
      :actions="frequencyOptions"
      cancel-text="取消"
      description="选择会议频率"
      close-on-click-action
      @select="onFrequencySelect"
    />

    <!-- 入会限制选择器 -->
    <van-action-sheet
      v-model:show="showJoinRestrictionPicker"
      :actions="joinRestrictionOptions"
      cancel-text="取消"
      description="选择入会限制"
      close-on-click-action
      @select="onJoinRestrictionSelect"
    />

    <van-overlay :show="loading" class="flex items-center justify-center bg-[rgba(255,255,255,0.7)]">
      <van-loading />
    </van-overlay>
  </div>
</template>

<script setup lang="ts">
import type { ScheduleMeetingForm as BaseScheduleMeetingForm } from '/#/meeting'
import type { ActionSheetAction as VantActionSheetAction } from 'vant'
import { Toast } from 'vant'

// Define the possible values for frequency
type MeetingFrequencyValue = 'once' | 'daily' | 'weekly' | 'monthly'

// Custom type for ActionSheet items with a value
interface CustomActionSheetAction extends VantActionSheetAction {
  value: MeetingFrequencyValue | string
}

// Explicitly define all properties for the form
// Omit properties from BaseScheduleMeetingForm that will be explicitly defined here with potentially different types (e.g. frequency)
// or that are not directly part of this form's direct state but from Base.
interface ExtendedScheduleMeetingForm
  extends Omit<
    BaseScheduleMeetingForm,
    'frequency' | 'startDate' | 'startTime' | 'endDate' | 'endTime' | 'password'
  > {
  title: string
  startDate: string
  startTime: string
  endDate: string
  endTime: string
  startDayOfWeek: string
  endDayOfWeek: string
  frequency: MeetingFrequencyValue
  password: string // Non-optional, will be initialized to empty string
  allowAllJoin: boolean
  joinRestriction: string
  enableWaitingRoom: boolean
  description?: string
}

const router = useRouter()
const loading = ref<boolean>(false)

// --- Time Picker State ---
const showStartTimePickerState = ref(false)
const showEndTimePickerState = ref(false)
const minCalendarDate = new Date() // Min date for any picker (today)

// Picker values for start time - Vant 4 format
// DatePicker expects ['2024', '04', '01'] format
// TimePicker expects ['14', '30'] format
const currentStartDateArray = ref<string[]>([])
const currentStartTimeArray = ref<string[]>([])
// Picker values for end time
const currentEndDateArray = ref<string[]>([])
const currentEndTimeArray = ref<string[]>([])
// Min date for end time picker
const minEndDate = ref(new Date())

const showFrequencyPicker = ref(false)
const frequencyOptions: CustomActionSheetAction[] = [
  { name: '单次会议', value: 'once' },
  { name: '每日会议', value: 'daily' }
]

const showJoinRestrictionPicker = ref(false)
const joinRestrictionOptions: CustomActionSheetAction[] = [
  { name: '所有人可入会', value: 'all' },
  { name: '仅企业内部用户', value: 'internal' },
  { name: '指定成员可入会', value: 'specific' }
]

const getDayOfWeek = (date: Date): string => {
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  return days[date.getDay()]
}

const formatDate = (date: Date): string => {
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${month}月${day}日`
}

const formatTime = (date: Date): string => {
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

// Helper to parse form date/time strings back to Date object
const parseFormDateTime = (dateStr: string, timeStr: string): Date => {
  const year = new Date().getFullYear()
  const month = parseInt(dateStr.substring(0, 2), 10) - 1
  const day = parseInt(dateStr.substring(3, 5), 10)
  const hours = parseInt(timeStr.substring(0, 2), 10)
  const minutes = parseInt(timeStr.substring(3, 5), 10)
  return new Date(year, month, day, hours, minutes)
}

// Helper to convert Date to Vant DatePicker format ['2024', '04', '01']
const dateToPickerFormat = (date: Date): string[] => {
  return [
    date.getFullYear().toString(),
    (date.getMonth() + 1).toString().padStart(2, '0'),
    date.getDate().toString().padStart(2, '0')
  ]
}

// Helper to convert time string to Vant TimePicker format ['14', '30']
const timeToPickerFormat = (timeStr: string): string[] => {
  const [hours, minutes] = timeStr.split(':')
  return [hours, minutes]
}

// Helper to convert picker format back to Date
const pickerFormatToDate = (dateArray: string[], timeArray: string[]): Date => {
  const year = parseInt(dateArray[0], 10)
  const month = parseInt(dateArray[1], 10) - 1 // JS months are 0-indexed
  const day = parseInt(dateArray[2], 10)
  const hours = parseInt(timeArray[0], 10)
  const minutes = parseInt(timeArray[1], 10)
  return new Date(year, month, day, hours, minutes)
}

// Helper to snap time to 15-minute intervals
const snapTimeToInterval = (timeStr: string): string => {
  const [hours, minutes] = timeStr.split(':').map(Number)
  const snappedMinutes = Math.ceil(minutes / 15) * 15
  const finalHours = snappedMinutes >= 60 ? hours + 1 : hours
  const finalMinutes = snappedMinutes >= 60 ? 0 : snappedMinutes
  return `${finalHours.toString().padStart(2, '0')}:${finalMinutes
    .toString()
    .padStart(2, '0')}`
}

const initialStartDate = new Date()
initialStartDate.setHours(15, 0, 0, 0)
const initialEndDate = new Date(initialStartDate.getTime() + 60 * 60 * 1000)

const form = reactive<ExtendedScheduleMeetingForm>({
  title: '王敏慧的主题会议',
  startDate: formatDate(initialStartDate),
  startTime: formatTime(initialStartDate),
  endDate: formatDate(initialEndDate),
  endTime: formatTime(initialEndDate),
  startDayOfWeek: getDayOfWeek(initialStartDate),
  endDayOfWeek: getDayOfWeek(initialEndDate),
  frequency: 'once',
  password: '',
  allowAllJoin: true,
  joinRestriction: 'all',
  enableWaitingRoom: false,
  description: ''
} as ExtendedScheduleMeetingForm)

// Initialize picker values from form
const initStartDateTime = parseFormDateTime(form.startDate, form.startTime)
const initEndDateTime = parseFormDateTime(form.endDate, form.endTime)

currentStartDateArray.value = dateToPickerFormat(initStartDateTime)
currentStartTimeArray.value = timeToPickerFormat(form.startTime)
currentEndDateArray.value = dateToPickerFormat(initEndDateTime)
currentEndTimeArray.value = timeToPickerFormat(form.endTime)
minEndDate.value = initStartDateTime

// Watch for start date/time changes to update min end date
watch(
  [currentStartDateArray, currentStartTimeArray],
  ([newStartDateArray, newStartTimeArray]) => {
    if (newStartDateArray.length === 3 && newStartTimeArray.length === 2) {
      const startDateTime = pickerFormatToDate(
        newStartDateArray,
        newStartTimeArray
      )

      // Set minimum end date to be at least 15 minutes after start
      minEndDate.value = new Date(startDateTime.getTime() + 15 * 60 * 1000)

      // If current end time is before new start time, adjust it
      if (
        currentEndDateArray.value.length === 3 &&
        currentEndTimeArray.value.length === 2
      ) {
        const endDateTime = pickerFormatToDate(
          currentEndDateArray.value,
          currentEndTimeArray.value
        )

        if (endDateTime <= startDateTime) {
          const newEndDateTime = new Date(
            startDateTime.getTime() + 60 * 60 * 1000
          )
          currentEndDateArray.value = dateToPickerFormat(newEndDateTime)
          currentEndTimeArray.value = timeToPickerFormat(
            formatTime(newEndDateTime)
          )
        }
      }
    }
  }
)

const selectedFrequencyText = computed(() => {
  return (
    frequencyOptions.find(opt => opt.value === form.frequency)?.name ||
    '单次会议'
  )
})

const selectedJoinRestrictionText = computed(() => {
  return (
    joinRestrictionOptions.find(opt => opt.value === form.joinRestriction)
      ?.name || '所有人可入会'
  )
})

const meetingDuration = computed(() => {
  const startDateObj = parseFormDateTime(form.startDate, form.startTime)
  const endDateObj = parseFormDateTime(form.endDate, form.endTime)

  if (
    isNaN(startDateObj.getTime()) ||
    isNaN(endDateObj.getTime()) ||
    endDateObj <= startDateObj
  ) {
    // Return a default or indicative string if times are invalid or not set properly
    // For example, if only start time is set, or end time is before start time.
    // For this UI, it always expects a duration. If it can be invalid, handle display here.
    const now = new Date()
    const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000)
    if (form.startTime && !form.endTime) return '1小时' // Default if only start time is there
    if (endDateObj <= startDateObj) return '无效'
    // Fallback to a sensible default if not fully initialized, though initial values should prevent this.
    return '1小时'
  }
  const diffMs = endDateObj.getTime() - startDateObj.getTime()
  const diffHours = diffMs / (1000 * 60 * 60)
  const diffMinutes = Math.round((diffMs % (1000 * 60 * 60)) / (1000 * 60))

  let durationStr = ''
  if (Math.floor(diffHours) > 0) {
    durationStr += `${Math.floor(diffHours)}小时`
  }
  if (diffMinutes > 0) {
    if (durationStr) durationStr += ' '
    durationStr += `${diffMinutes}分钟`
  }
  return durationStr || '0分钟'
})

const onBack = () => {
  router.back()
}

const handleSubmit = () => {
  loading.value = true
  console.log('提交预约会议:', form)
  // Password can be empty, but if not, must be 4-6 digits.
  if (form.password && (form.password.length < 4 || form.password.length > 6)) {
    Toast.fail('密码长度必须为4-6位')
    return
  }
  // Validate that end time is after start time
  const startDateObj = parseFormDateTime(form.startDate, form.startTime)
  const endDateObj = parseFormDateTime(form.endDate, form.endTime)
  if (endDateObj <= startDateObj) {
    Toast.fail('结束时间必须晚于开始时间')
    return
  }
  router.back()
}

const openStartTimePicker = () => {
  // Set current values from form
  const startDateTime = parseFormDateTime(form.startDate, form.startTime)
  currentStartDateArray.value = dateToPickerFormat(startDateTime)
  currentStartTimeArray.value = timeToPickerFormat(form.startTime)
  showStartTimePickerState.value = true
}

const onStartTimeConfirm = () => {
  if (
    currentStartDateArray.value.length === 3 &&
    currentStartTimeArray.value.length === 2
  ) {
    // Convert picker format back to datetime
    const selectedDateTime = pickerFormatToDate(
      currentStartDateArray.value,
      currentStartTimeArray.value
    )

    // Snap time to 15-minute intervals
    const timeStr = formatTime(selectedDateTime)
    const snappedTime = snapTimeToInterval(timeStr)

    // Update form values
    form.startDate = formatDate(selectedDateTime)
    form.startTime = snappedTime
    form.startDayOfWeek = getDayOfWeek(selectedDateTime)

    // Update end time if it's now invalid
    const endDateTime = parseFormDateTime(form.endDate, form.endTime)
    if (endDateTime <= selectedDateTime) {
      const newEndDateTime = new Date(
        selectedDateTime.getTime() + 60 * 60 * 1000
      )
      form.endDate = formatDate(newEndDateTime)
      form.endTime = formatTime(newEndDateTime)
      form.endDayOfWeek = getDayOfWeek(newEndDateTime)
    }
  }

  showStartTimePickerState.value = false
}

const openEndTimePicker = () => {
  // Set current values from form
  const startDateTime = parseFormDateTime(form.startDate, form.startTime)
  minEndDate.value = new Date(startDateTime.getTime() + 15 * 60 * 1000)

  const endDateTime = parseFormDateTime(form.endDate, form.endTime)
  currentEndDateArray.value = dateToPickerFormat(endDateTime)
  currentEndTimeArray.value = timeToPickerFormat(form.endTime)
  showEndTimePickerState.value = true
}

const onEndTimeConfirm = () => {
  if (
    currentEndDateArray.value.length === 3 &&
    currentEndTimeArray.value.length === 2
  ) {
    // Convert picker format back to datetime
    const selectedDateTime = pickerFormatToDate(
      currentEndDateArray.value,
      currentEndTimeArray.value
    )

    // Snap time to 15-minute intervals
    const timeStr = formatTime(selectedDateTime)
    const snappedTime = snapTimeToInterval(timeStr)

    // Validate end time is after start time
    const startDateTime = parseFormDateTime(form.startDate, form.startTime)
    if (selectedDateTime <= startDateTime) {
      Toast.fail('结束时间必须晚于开始时间')
      showEndTimePickerState.value = false
      return
    }

    // Update form values
    form.endDate = formatDate(selectedDateTime)
    form.endTime = snappedTime
    form.endDayOfWeek = getDayOfWeek(selectedDateTime)
  }

  showEndTimePickerState.value = false
}

const onFrequencySelect = (action: CustomActionSheetAction) => {
  form.frequency = action.value as MeetingFrequencyValue
}

const onJoinRestrictionSelect = (action: CustomActionSheetAction) => {
  form.joinRestriction = action.value as string
  form.allowAllJoin = action.value === 'all'
}
</script>

<style lang="less" scoped>
.schedule-meeting-page {
  .van-nav-bar {
    ::v-deep(.van-nav-bar__title) {
      font-weight: 500;
    }
    ::v-deep(.van-nav-bar__right) {
      .text-blue-500 {
        cursor: pointer;
        &:active {
          opacity: 0.7;
        }
      }
    }
  }

  .meeting-title-field {
    padding: 0;
    ::v-deep(.van-field__control) {
      font-size: 18px;
      font-weight: 500;
      line-height: 1.5;
    }
    ::v-deep(.van-field__body) {
      padding: 0;
      border: none;
    }
  }

  .custom-van-field {
    padding-left: 0;
    padding-right: 0;
    ::v-deep(.van-field__label) {
      color: #374151;
      font-size: 0.875rem;
      margin-right: 0;
    }
    ::v-deep(.van-field__control--right) {
      color: #6b7280;
      font-size: 0.875rem;
    }
    &::after {
      border-bottom: none;
    }
  }

  .border-b:last-child,
  .border-t:first-child {
    border: none;
  }

  .items-center.justify-between.py-3,
  .flex-1[role='button'] {
    cursor: pointer;
    &:active {
      background-color: #f9fafb;
    }
  }
  :deep(.van-cell-group) {
    margin: 0;
  }
}
</style>
