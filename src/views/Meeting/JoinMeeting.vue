<template>
  <div class="join-meeting-page bg-gray-100 min-h-screen">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="加入会议"
      left-arrow
      @click-left="onBack"
      class="custom-nav-bar"
    />

    <div class="px-4 py-6">
      <!-- 会议信息卡片 -->
      <div class="meeting-card">
        <!-- 会议号输入 -->
        <div class="meeting-number-section mb-6 bg-white p-3 rounded-md">
          <div class="flex items-center">
            <van-field
              label="会议号"
              id="meetingNumberInput"
              v-model="meetingNumber"
              placeholder="请输入会议号"
              :border="false"
              class="custom-van-field flex-grow p-0"
              maxlength="15"
            />
          </div>
        </div>

        <!-- 加入会议按钮 -->
        <div class="mb-6">
          <van-button
            type="primary"
            block
            class="join-van-button"
            :disabled="!meetingNumber"
            :loading="loading"
            @click="handleJoinMeeting"
          >
            加入会议
          </van-button>
        </div>

        <!-- 入会选项 -->
        <div class="join-options-section">
          <div class="text-gray-500 text-sm mb-2">入会选项</div>

          <div class="space-y-0 bg-white rounded-md">
            <van-cell-group inset>
              <van-cell center title="入会开启麦克风">
                <template #right-icon>
                  <van-switch
                    v-model="joinOptions.enableMicrophone"
                    size="22px"
                  />
                </template>
              </van-cell>

              <van-cell center title="入会开启扬声器">
                <template #right-icon>
                  <van-switch v-model="joinOptions.enableSpeaker" size="22px" />
                </template>
              </van-cell>

              <van-cell center title="入会开启摄像头">
                <template #right-icon>
                  <van-switch v-model="joinOptions.enableCamera" size="22px" />
                </template>
              </van-cell>
            </van-cell-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { JoinOptions } from '/#/meeting'

const router = useRouter()
const meetingNumber = ref<string>('')
const loading = ref<boolean>(false)

// 入会选项
const joinOptions = reactive<JoinOptions>({
  enableMicrophone: true,
  enableSpeaker: true,
  enableCamera: false
})

const onBack = () => {
  router.back()
}

const handleJoinMeeting = () => {
  if (!meetingNumber.value) {
    return
  }
  loading.value = true
  console.log('加入会议:', {
    meetingNumber: meetingNumber.value,
    options: joinOptions
  })

  // 实际场景中这里会调用加入会议的API
  // 可能需要跳转到会议室页面
}
</script>

<style lang="less" scoped>
.join-meeting-page {
  :deep(.custom-nav-bar) {
    background-color: #f3f4f6;
    .van-nav-bar__title {
      color: #333;
      font-weight: 600;
      font-size: 17px;
    }
    .van-icon {
      color: #333;
    }
  }

  :deep(.custom-van-field) {
    .van-field__control {
      text-align: left; // 根据UI图，placeholder靠左
      font-size: 16px; // 与UI图的文字大小匹配
      color: #333; // 输入文字颜色
      &::placeholder {
        color: #c8c9cc;
        font-weight: normal;
      }
    }
    // 移除van-field默认的下边框和padding
    padding: 0;
    &.van-field {
      padding-left: 0; // 确保placeholder左对齐
    }
  }

  :deep(.van-cell-group) {
    margin: 0;
  }
}
</style>
