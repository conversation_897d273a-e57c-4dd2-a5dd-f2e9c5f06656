<template>
  <div class="flex flex-col h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <van-nav-bar title="易联会议Demo" left-arrow @click-left="closeNav"/>

    <!-- 页面内容区域 (导航栏下方开始) -->
    <div class="flex flex-col flex-grow flex-1 overflow-hidden"> 
      <!-- 会议号输入区域 -->
      <div class="px-4 py-3 bg-white shrink-0">
        <van-field
          v-model="meetingNumber"
          placeholder="会议号"
          class="!rounded-md !bg-gray-100 !py-2 !px-4" 
          input-align="center"
          clearable
        />
      </div>

      <!-- 功能按钮 -->
      <div class="flex justify-around items-center p-4 bg-white shadow-sm shrink-0">
        <div class="flex flex-col items-center text-center w-24 gap-[5px]" @click="goToSchedule">
          <img src="@/assets/image/schedule-meeting.png" alt="预约会议" class="w-12 h-12">
          <span class="text-sm text-gray-700">预约会议</span>
        </div>
        
        <div class="flex flex-col items-center text-center w-24 gap-[5px]" @click="goToJoin">
          <img src="@/assets/image/join-meeting.png" alt="加入会议" class="w-12 h-12">
          <span class="text-sm text-gray-700">加入会议</span>
        </div>
      </div>

      <!-- 会议列表 -->
      <div class="meeting-list-scroll-container flex-grow overflow-y-auto p-4 mt-2 flex-1 h-full">
        <template v-if="meetingList.length > 0">
          <MeetingItem
            v-for="meeting in meetingList"
            :key="meeting.id"
            :meeting="meeting"
            @join="handleJoinMeeting"
            class="mb-3" 
          />
        </template>
        <van-empty v-else description="暂无会议" class="bg-white rounded-lg shadow-sm"/>
      </div>
    </div>
    <van-overlay :show="loading" class="flex items-center justify-center bg-[rgba(255,255,255,0.7)]">
      <van-loading />
    </van-overlay>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import MeetingItem from '@/components/Meeting/MeetingItem.vue'
import type { Meeting } from '/#/meeting'
import { cordovaMideaCommon } from '@/utils/cordova'

const router = useRouter()
const meetingNumber = ref<string>('')
const meetingList = ref<Meeting[]>([])
const loading = ref<boolean>(false)

// 模拟会议数据
const mockMeetings: Meeting[] = [
  {
    id: '1',
    title: 'T信周会',
    date: '03月24日',
    time: '09:30-10:30',
    host: '王敏慧',
    meetingNumber: '856 680 68136',
    status: '进行中',
    isRecurring: false
  },
  {
    id: '2',
    title: 'T信周会',
    date: '03月31日',
    time: '09:30-10:30',
    frequency: '周期',
    host: '王敏慧',
    meetingNumber: '856 680 68136',
    status: '未开始',
    isRecurring: true
  },
  {
    id: '3',
    title: 'T信周会',
    date: '04月07日',
    time: '09:30-10:30',
    host: '王敏慧',
    meetingNumber: '856 680 68136',
    status: '未开始',
    isRecurring: false
  },
  {
    id: '4',
    title: 'T信周会',
    date: '04月14日',
    time: '09:30-10:30',
    host: '王敏慧',
    meetingNumber: '856 680 68136',
    status: '未开始',
    isRecurring: false
  },
  {
    id: '5',
    title: 'T信周会',
    date: '04月14日',
    time: '09:30-10:30',
    host: '王敏慧',
    meetingNumber: '856 680 68136',
    status: '未开始',
    isRecurring: false
  },
  {
    id: '6',
    title: 'T信周会',
    date: '04月14日',
    time: '09:30-10:30',
    host: '王敏慧',
    meetingNumber: '856 680 68136',
    status: '未开始',
    isRecurring: false
  }
]

onMounted(() => {
  meetingList.value = mockMeetings
})

const goToSchedule = () => {
  router.push('/meeting/schedule')
}

const goToJoin = () => {
  router.push('/meeting/join')
}

const handleJoinMeeting = (meeting: Meeting) => {
  console.log('加入会议:', meeting)
  loading.value = true
  // 实际场景中这里会调用加入会议的API
}

const closeNav = () => {
  cordovaMideaCommon('exit', [])
}
</script>