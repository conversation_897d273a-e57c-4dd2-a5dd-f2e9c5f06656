import type { App } from 'vue'
import type { LocaleType } from '/#/config'
import type { I18nOptions } from 'vue-i18n'
import { createI18n } from 'vue-i18n'
import { setHtmlPageLang } from './helper'
import { useUserStoreWithOut } from '@/store/modules/userStore'
import { localeSetting, LOCALE } from '@/config/localeSetting'
import { Locale } from 'vant'
import enUS from './lang/en-US'
import zhCN from './lang/zh-CN'
import vantEnUS from 'vant/lib/locale/lang/en-US'
import vantZhCN from 'vant/lib/locale/lang/zh-CN'

export let i18n: ReturnType<typeof createI18n>
async function createI18nOptions(): Promise<I18nOptions> {
  const localeStore = useUserStoreWithOut()
  const locale = localeStore.getLocale
  const { fallback, availableLocales } = localeSetting
  setHtmlPageLang(locale)
  // setVantLocale(locale);
  return {
    legacy: false,
    locale,
    fallbackLocale: fallback,
    messages: {
      [LOCALE.EN_US]: enUS.message ?? {},
      [LOCALE.ZH_CN]: zhCN.message ?? {}
    },
    availableLocales: availableLocales,
    sync: true, //If you don’t want to inherit locale from global scope, you need to set sync of i18n component option to false.
    silentTranslationWarn: true, // true - warning off
    missingWarn: false,
    silentFallbackWarn: true
  }
}

export async function setupI18n(app: App) {
  const options = await createI18nOptions()
  i18n = createI18n(options)
  app.use(i18n)
}

export function setI18nLanguage(locale: LocaleType) {
  const localeStore = useUserStoreWithOut();

  if (i18n.mode === "legacy") {
    i18n.global.locale = locale;
  } else {
    (i18n.global.locale as any).value = locale;
  }
  localeStore.setLocale(locale);
  setHtmlPageLang(locale);
  setVantLocale(locale);
}

function setVantLocale(locale: LocaleType) {
  if (locale === LOCALE.ZH_CN) {
    Locale.use(locale, vantZhCN);
  } else {
    Locale.use(locale, vantEnUS);
  }
}
