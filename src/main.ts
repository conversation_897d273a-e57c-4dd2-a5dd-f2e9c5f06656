import { createApp } from 'vue'
import './buffer'
import './style.css'
import App from './App.vue'
import './style/vant.style'
import 'virtual:svg-icons-register'
// import { getUser } from "@/utils/cordova";
import { router, setupRouter } from '@/router/index'
import { setupRouterGuard } from '@/router/guard/index'
import { setupVconsole } from '@/utils/vconsole'
import { setupPinia } from '@/store/index'
import { setupI18n } from '@/locales/index'
import { setupUserInfo, setupLanguage } from '@/utils/adapterTClient'
import SvgIcon from '@/components/icon/SvgIcon.vue'

import 'virtual:uno.css'

async function bootstrap() {
  const app = createApp(App)
  console.log('%csrc/main.ts:20 import.meta.env.NODE_ENV', 'color: #007acc;', import.meta.env);
  // 设置vconsole
  if (import.meta.env.MODE !== 'production') {
    await setupVconsole()
  }

  // 设置多语言
  await setupI18n(app)

  // 设置pinia
  setupPinia(app)
  app.component(SvgIcon?.name ?? 'SvgIcon', SvgIcon)

  // 设置用户信息
  await setupUserInfo()

  // 设置语言
  await setupLanguage()

  // 配置路由
  setupRouter(app)

  // 配置路由守卫
  setupRouterGuard(router)

  app.mount('#app')
}
bootstrap()
