<template>
  <section class="t-layout">
    <slot name="header"></slot>
    <main>
      <slot></slot>
    </main>
    <slot name="footer"></slot>
  </section>
</template>

<script setup lang="ts"></script>

<style lang="less" scoped>
.t-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  main {
    // padding: 16px 16px 0 16px;
    flex: 1 1 0;
    overflow-y: auto;
  }
}
</style>