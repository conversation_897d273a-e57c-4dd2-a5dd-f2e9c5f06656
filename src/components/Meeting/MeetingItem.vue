<template>
  <div
    class="meeting-item bg-white rounded-md p-4 shadow-sm hover:shadow-md transition-shadow duration-200 flex flex-col"
  >
    <div class="flex items-start justify-between">
      <!-- 左侧会议信息 -->
      <div class="flex-1 min-w-0 pr-3">
        <div class="flex items-center mb-1.5">
          <h3 class="text-base font-semibold text-gray-800 mr-2 truncate m-0">
            {{ meeting.title }}
          </h3>
          <van-tag
            v-if="meeting.status === '进行中'"
            plain
            size="medium"
            type="success"
          >
            {{ meeting.status }}
          </van-tag>
        </div>

        <div class="text-sm text-gray-500 mb-1">
          {{ meeting.date }} {{ meeting.time }} {{ meeting.frequency }}
        </div>
      </div>

      <!-- 右侧播放按钮 -->
      <div class="flex-shrink-0 self-center">
        <div
          class="w-10 h-10 bg-gray-50 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-200 transition-colors"
          @click="$emit('join', meeting)"
        >
          <van-icon name="play" size="20" color="#333" />
        </div>
      </div>
    </div>
    <div class="text-xs text-gray-400">
      <span>发起人：{{ meeting.host }}</span>
      <span class="mx-1.5">|</span>
      <span>会议号：{{ meeting.meetingNumber }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Meeting } from '../../../types/meeting'

interface Props {
  meeting: Meeting
}

interface Emits {
  (e: 'join', meeting: Meeting): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

<style lang="less" scoped>
.meeting-item {
  .text-xs span {
    display: inline-block;
    vertical-align: middle;
  }
}
</style>
