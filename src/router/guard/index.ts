import type { Router, RouteLocationNormalized } from 'vue-router'
import { isTLinkPC, isTLinkMobile } from '@/utils/is'
import { isPCOrIPad } from '@/utils/is'

// console.log('store', store)
export function setupRouterGuard(router: Router) {
  createPCTokenGuard(router)
  createMobileTokenGuard(router)
  createBrowserTokenGuard(router)
  // createDeviceRedirectGuard(router)
}
// PC token和用户信息处理
function createPCTokenGuard(router: Router) {
  router.beforeEach(async (to, form, next) => {
    if (isTLinkPC()) {
      next()
    } else {
      next()
    }
  })
}
// 移动端token处理
function createMobileTokenGuard(router: Router) {
  router.beforeEach(async (to, form, next) => {
    if (isTLinkMobile()) {
      next()
    } else {
      next()
    }
  })
}

// 浏览器打开token处理

function createBrowserTokenGuard(router: Router) {
  router.beforeEach(async (to, form, next) => {
    next()
  })
}

// 根据设备类型重定向到相应页面
// function createDeviceRedirectGuard(router: Router) {
//   router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized, next) => {
//     // 检查设备类型并进行适当重定向
//     if (isPCOrIPad()) {
//       // PC或iPad设备
//       if (to.path === '/mobile') {
//         next({ path: '/' })
//         return
//       }
//     } else {
//       if (to.path === '/detail') {
//         next()
//         return
//       }
//       // 移动设备
//       if (to.path === '/' || to.path !== '/mobile') {
//         next({ path: '/mobile' })
//         return
//       }
//     } 
//     next()
//   })
// }
