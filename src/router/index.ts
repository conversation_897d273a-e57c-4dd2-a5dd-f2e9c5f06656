import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import type { App } from 'vue'
const routes: RouteRecordRaw[] = [
  {
    name: '404',
    path: '/404',
    component: () => import('@/views/404/index.vue')
  },
  {
    name: 'iconview',
    path: '/iconview',
    component: () => import('@/views/IconView/index.vue')
  },
  {
    name: 'index',
    path: '/',
    component: () => import('@/views/Meeting/MeetingList.vue')
  },
  {
    name: 'join-meeting',
    path: '/meeting/join',
    component: () => import('@/views/Meeting/JoinMeeting.vue')
  },
  {
    name: 'schedule-meeting',
    path: '/meeting/schedule',
    component: () => import('@/views/Meeting/ScheduleMeeting.vue')
  },
]

export const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export function setupRouter(app: App<Element>) {
  app.use(router)
}
