// 会议状态类型
export type MeetingStatus = '进行中' | '未开始' | '已结束'

// 会议类型
export interface Meeting {
  id: string
  title: string
  date: string
  time: string
  host: string
  meetingNumber: string
  status: MeetingStatus
  isRecurring: boolean
  frequency?: string
  description?: string
  password?: string
}

// 入会选项
export interface JoinOptions {
  enableMicrophone: boolean
  enableSpeaker: boolean
  enableCamera: boolean
}

// 预约会议表单
export interface ScheduleMeetingForm {
  title: string
  startDate: string
  startTime: string
  endTime: string
  frequency: 'once' | 'daily' | 'weekly' | 'monthly'
  password: string
  allowAllJoin: boolean
  enableWaitingRoom: boolean
  description?: string
} 