export type LocaleType = 'zh-CN' | 'en-US'
export interface LocaleSetting {
  // Current language
  locale: LocaleType
  // default language
  fallback: LocaleType
  // available Locales
  availableLocales: LocaleType[]
}

export interface UserInfo<K extends keyof any, T = any> {
  departmentName: string
  extra?: string
  uid: string
  dept: string
  mobile: string
  employeenumber: string
  ssoToken?: string
  positionName: string
  fullDeptName: string
  telephonenumber: string
  mail: string
  ou?: string
  cn: string
  uniqueIdentifier: string
  gender: string
  eip?: string
  cookie?: string
  sessionkey?: string
  token: string
  showGradeFlag?: boolean
  income?: number
  gradeLevel: number
  appId: string
  sessionKey: string
  uphone: string
  usex: string
  uname: string
  [P in K]: T
}
