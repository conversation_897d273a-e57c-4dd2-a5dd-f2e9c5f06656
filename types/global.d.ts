import type {
  ComponentR<PERSON>Proxy,
  VNode,
  VNodeChild,
  ComponentPublicInstance,
  FunctionalComponent,
  PropType as VuePropType
} from 'vue'

interface UserInfo {
  showGradeFlag: boolean
  income: number
  code: string
  customized: boolean
  signature: string
  nextLevelNeedScore: number
  gradeTitleCn: string
  orgList: Array<any>
  gradeTitleEn: string
  positionName: string
  gradeLevel: number
  empNum: string
  headerFrame: string
  balance: number
  accountUsername: string
  codeName: string
  workingStatus?: any
  email: string
  gradeName: string
  namePath: string
  accountType: string
  mobile: string
  headurl: string
  staffType: number
  muti: number
  empnum: string
  codePath: string
  statusId?: undefined
  expend: number
  name: string
  username: string
}
interface Permissions {
  groupChat: boolean
  groupCreate: boolean
  groupInvite: boolean
  file: boolean
  sendMedia: boolean
  saveMedia: boolean
  gobalWatermark: boolean
  selfOrganizing: boolean
  picWatermark: boolean
  screenshot: boolean
  decode: boolean
  showNum: boolean
  sendPicWay: boolean
  voiceCall: boolean
  bSunFlower: boolean
}
// pc注入方法
interface Tchat {
  getUser(): UserInfo
  getChatPermissions(): Permissions
  copyImg(): Promise<T>
  showSaveDialog(): Promise<T>
}
interface CallPc {
  callTlinkpc: (method: string) => Promise<T>
  on: (event: string, callback: () => void) => void
}
declare global {
  interface Cordova {
    exec(success: () => void, error: () => any)
  }

  interface Window {
    tchat: Tchat
    cordova: {
      exec<T = unknown>(
        success: (err: unknown) => void,
        error: (err: unknown) => void,
        classify: string,
        method: string,
        params?: T[]
      )
    }
    callPc: CallPc
  }
  declare type Recordable<T = any> = Record<string, T>
}

declare module 'vue' {
  export type JSXComponent<Props = any> =
    | { new (): ComponentPublicInstance<Props> }
    | FunctionalComponent<Props>
}


declare module 'crypto-browserify'

declare module 'crypto' {
  import crypto from 'crypto'
  export default crypto
}