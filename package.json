{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:prod": "vite --mode dev-prod", "build": "vue-tsc && vite build", "build:test": "vue-tsc && vite build --mode prod-test", "build:prod": "vue-tsc && vite build --mode production", "preview": "vite preview", "format": "prettier --write --parser typescript \"(src|types)/**/*.ts\""}, "dependencies": {"@types/crypto-js": "^4.2.2", "@vueuse/core": "^9.13.0", "@vueuse/rxjs": "^12.7.0", "@ylink-sdk/mobile-web": "^1.0.0", "@ylink-sdk/web": "^1.0.3", "ant-design-vue": "4.x", "axios": "^1.3.3", "buffer": "^6.0.3", "clipboard": "^2.0.11", "crypto": "^1.0.1", "crypto-browserify": "^3.12.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "js-cookie": "^3.0.5", "less": "^4.1.3", "lodash-es": "^4.17.21", "pinia": "^2.0.32", "qs": "^6.11.2", "rxjs": "^7.8.1", "stream-browserify": "^3.0.0", "vant": "^4.4.1", "viewerjs": "^1.11.3", "vite-plugin-commonjs": "^0.7.1", "vite-plugin-node-polyfills": "^0.23.0", "vue": "^3.4.0", "vue-i18n": "9", "vue-request": "2.0.0-rc.4", "vue-router": "^4.1.6"}, "devDependencies": {"@types/js-cookie": "^3.0.3", "@types/lodash-es": "^4.17.6", "@types/node": "^20.17.19", "@types/qs": "^6.9.7", "@unocss/preset-wind": "^0.58.0", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-legacy": "^4.0.1", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "postcss-preset-env": "^8.0.1", "prettier": "^2.8.4", "rollup-plugin-compression": "^1.0.2", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-polyfill-node": "^0.12.0", "typescript": "^4.9.3", "unocss": "^0.58.0", "unplugin-auto-import": "^19.1.0", "unplugin-vue-components": "^0.24.0", "vconsole": "^3.15.0", "vite": "^5.0.7", "vite-plugin-commonjs-externals": "^0.1.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.14.7", "vite-plugin-require": "^1.1.10", "vite-plugin-require-transform": "^1.0.12", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-zip-pack": "^1.0.5", "vue-tsc": "^1.0.24"}, "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verify-commit-msg.js"}, "lint-staged": {"*.js": ["prettier --write"], "*.ts": ["prettier --parser=typescript --write"]}, "volta": {"node": "16.14.1"}}